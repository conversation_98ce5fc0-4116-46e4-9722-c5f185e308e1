{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "Discover great apps, games, extensions and themes for Google Chrome.", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "Web Store", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\137.0.7151.120\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\137.0.7151.120\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "4C8E0089B51FA82E7693C70C18897DEBDC3BC34A1EFAB0B3246D13ADE3083E97"}, "extensions": {"ui": {"developer_mode": "A98B1CD0D533165D3ABEBA398D82E0B8564E30CCCEF0711E4182F6AA9FA497EA"}}, "homepage": "1BD7E4C8520B1186C7219CFC1B8F575684BAD525CE448D698A0C2D6F87BAA959", "homepage_is_newtabpage": "83C92C57F5EEE0B52AC8904D0CD7D87AE800B95EA95FC17378E1EA2D3A10A689", "session": {"restore_on_startup": "4B0F9BE8D5F5FFE5E2147BF961FE86C5C5D871C3E48AA5E5B0A8DCD1E771ACE3", "startup_urls": "EA33424B22F0DD08A6B4671ABCA5258E5E53FAB3D87F0ACC8F7A766044B92F6E"}}, "browser": {"show_home_button": "BF2B42BD6A0D2DF12C12D99A77CD94A16202DAE41A178E8D041F0CBBA7D0AB9C"}, "default_search_provider_data": {"template_url_data": "809957B77009BE7501828AD1EF6FD6635ED91E6AE339C171EF39CCFE8737C2E2"}, "enterprise_signin": {"policy_recovery_token": "01D90DB1708848ECE9018655C83907B670BD618662C4E13DE372C03BF6AD83D0"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "D4732D91F6720DB22F02C3FEA910BD3BD1E0DF8680AC72D5DD284BB40C1D27C0", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "40EF1211DC1F098D6488798C19ED28F6594FA7C9DA14DF524D20358253FB9BC8"}, "ui": {"developer_mode": "8ADF306F434F68200ABA362C2D3D5F3EF44659375892F79DBF302C3C286849EE"}}, "google": {"services": {"account_id": "B618F163EBF262C2C019A044B96931EDD31CC76ED78D6EF6D76156E30B84ED4D", "last_signed_in_username": "3624F8DCF35997652BDCE9328B14EE23A5070D13603FF7B683234FF5F2562E16", "last_username": "F7829E712A7A32F698BD0D008F60D5CED547D8DC2D52EDB827FDAB50EE7AC2F1"}}, "homepage": "9E9108B55FC7D7F675D5335ECE32C8B73ECE2E9354FCDAADA12A70381369636B", "homepage_is_newtabpage": "845CB482FF421BE8CC0D268DFB9972876920AA9A1DC44F94BE65CC0078FAE53E", "media": {"cdm": {"origin_data": "0E03D80C7973A67931E8DE84E4E86B76DBD97EF30D6BBEEAAC88EA3BBF5DB7E8"}, "storage_id_salt": "7435799C809458189426082C00F6B8B02DD476EE0BB207C3D41B90EAD06387C7"}, "module_blocklist_cache_md5_digest": "39DD30240C360C3534A71B93317FA9AC8FFB201AC2D6EADD25416E1DE1B8E308", "pinned_tabs": "800EA5CED55DDF4F2A210F8BCC04CF7FB02DA943B7305678BFA6AF5E9082CCB0", "prefs": {"preference_reset_time": "B445AD8BF7E84C104E10DF2D1972CF9DC9FCC07E917D87C613F174613C017F02"}, "safebrowsing": {"incidents_sent": "B683D197C7352E1CAB6F08E259D2405DBA83E0CA00217344DB03C15BB8F3955A"}, "search_provider_overrides": "A50416E30EB456ED0E78F7907E2013D2A97F549AFEA81507A6226C9CF5F3C1A4", "session": {"restore_on_startup": "E5A9C9D33392FAD454EEEABDF7AA1289C276B4658AA1786C03A268130B56B728", "startup_urls": "8D7590CBFD2A3415E7B323AA0C364D439DA4968C9C5837F204B6932B48CA9E58"}}, "super_mac": "558E0AAD65DA0D6256D42E46E0CD8D9B8873D00880534214CBF86A20866ECA21"}}