{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_tracker_service_last_update": "*****************", "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1090, "left": 10, "maximized": true, "right": 1930, "top": 10, "work_area_bottom": 1032, "work_area_left": 0, "work_area_right": 1920, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_search_provider": {"guid": ""}, "domain_diversity": {"last_reporting_timestamp": "*****************"}, "enterprise_profile_guid": "c1ea4d08-6799-4e6c-84b9-f4d6ac5576af", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}}, "gaia_cookie": {"changed_time": **********.827207, "hash": "2jmj7l5rSw0yVb/vlWAYkK/YBwk=", "last_list_accounts_binary_data": ""}, "gcm": {"product_category_for_subtypes": "com.chrome.windows"}, "google": {"services": {"signin_scoped_device_id": "e7f17fa9-5c19-444e-ba23-9cebd941cfb3"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "Glic": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "GlicAppMenuNewBadge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "GlicKeyboardShortcutNewBadge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13395106321559045", "recent_session_start_times": ["13395106321559045"], "session_last_active_time": "13395106321559045", "session_start_time": "13395106321559045"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_topics_to_handler": {"1013309121859": {}}}, "media": {"device_id_salt": "E082FC52DDC671DF72529EB6FCDF4863", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "VF4n9X9D4MSyFIDsn8jd3Yg+w3NVNQrDio4uTtKF0OWyTO/zbPB/Z/d4MF/28iXqPQA/IVqqTw2qEVoC5qyWeg=="}, "ntp": {"num_personal_suggestions": 1}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "GLIC_CONTEXTUAL_CUEING": true, "GLIC_ZERO_STATE_SUGGESTIONS": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false}, "privacy_sandbox": {"fake_notice": {"prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://studio.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": false}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395106321648944e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395106322937052e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 3.0, "rawScore": 3.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "137.0.7151.120", "creation_time": "*****************", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Normal", "family_member_role": "not_in_family", "last_engagement_time": "13395106322937053", "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Person 2", "password_hash_data_list": []}, "safebrowsing": {"event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13395365521808896", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13395106321", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"uma_in_sql_start_time": "13395106321551974"}, "sessions": {"event_log": [{"crashed": false, "time": "13395106321550641", "type": 0}, {"did_schedule_command": true, "first_session_service": true, "tab_count": 1, "time": "13395106325956200", "type": 2, "window_count": 1}], "session_data_status": 3}, "settings": {"force_google_safesearch": false}, "signin": {"allowed": true, "cookie_clear_on_exit_migration_notice_complete": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "passwords_per_account_pref_migration_done": true}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true, "tab_search_migration_complete": true}, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"amazon prime day 2025 deals\",\"nickelodeon kids choice awards winners\",\"irs $3000 tax refund amount\",\"balatro 1.1\",\"florida panthers parade fort lauderdale\",\"heat wave weather forecast\",\"new retirement age 2026 social security\",\"kpop demon hunters\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChoIkk4SFQoRVHJlbmRpbmcgc2VhcmNoZXMoCg\\u003d\\u003d\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\\u003d\",\"zl\":10002}],\"google:suggesteventid\":\"6439346105431322180\",\"google:suggestrelevance\":[1257,1256,1255,1254,1253,1252,1251,1250],\"google:suggestsubtypes\":[[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\"]}]"}}