{"NewTabPage": {"PrevNavigationTime": "*****************"}, "accessibility": {"captions": {"headless_caption_enabled": false, "live_caption_language": "en-US"}}, "account_info": [{"access_point": 31, "account_id": "115880785246047860713", "accountcapabilities": {"accountcapabilities/g42tslldmfya": 1, "accountcapabilities/g44tilldmfya": 0, "accountcapabilities/ge2dinbnmnqxa": 0, "accountcapabilities/ge2tkmznmnqxa": 1, "accountcapabilities/ge2tknznmnqxa": 1, "accountcapabilities/ge2tkobnmnqxa": 1, "accountcapabilities/ge3dgmjnmnqxa": 1, "accountcapabilities/ge3dgobnmnqxa": 1, "accountcapabilities/geydgnznmnqxa": 1, "accountcapabilities/geytcnbnmnqxa": 1, "accountcapabilities/gezdcnbnmnqxa": 1, "accountcapabilities/gezdsmbnmnqxa": 0, "accountcapabilities/geztenjnmnqxa": 1, "accountcapabilities/gi2tklldmfya": 1, "accountcapabilities/gu2dqlldmfya": 1, "accountcapabilities/gu4dmlldmfya": 0, "accountcapabilities/guydolldmfya": 0, "accountcapabilities/guzdslldmfya": 0, "accountcapabilities/haytqlldmfya": 1, "accountcapabilities/he4tolldmfya": 0}, "email": "<EMAIL>", "full_name": "Liquid One", "gaia": "115880785246047860713", "given_name": "Liquid", "hd": "NO_HOSTED_DOMAIN", "is_supervised_child": 0, "is_under_advanced_protection": false, "last_downloaded_image_url_with_size": "https://lh3.googleusercontent.com/a/ACg8ocKuUpnAKWVxAHi9MeHn0_ZHP2aAHot-XTOBf3S_gChNCcR3Lg=s256-c-ns", "locale": "en", "picture_url": "https://lh3.googleusercontent.com/a/ACg8ocKuUpnAKWVxAHi9MeHn0_ZHP2aAHot-XTOBf3S_gChNCcR3Lg=s96-c"}], "account_tracker_service_last_update": "*****************", "account_values": {"credentials_enable_autosignin": true, "credentials_enable_service": true, "sync": {"demographics": {"birth_year": 1988, "gender": 1}}}, "alternate_error_pages": {"backup": true}, "autocomplete": {"retention_policy_last_version": 137}, "autofill": {"last_version_deduped": 137}, "bookmark": {"storage_computation_last_update": "*****************"}, "browser": {"has_seen_welcome_page": false, "window_placement": {"bottom": 1080, "left": 1913, "maximized": true, "right": 3833, "top": 0, "work_area_bottom": 1032, "work_area_left": 1920, "work_area_right": 3840, "work_area_top": 0}}, "commerce_daily_metrics_last_update_time": "*****************", "countryid_at_install": 21843, "default_apps_install_state": 3, "default_search_provider": {"guid": ""}, "devtools": {"last_open_timestamp": "**************", "preferences": {"closeable-tabs": "{\"security\":true,\"freestyler\":true,\"chrome-recorder\":true}", "currentDockState": "\"right\"", "elements.styles.sidebar.width": "{\"vertical\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspector-view.split-view-state": "{\"vertical\":{\"size\":0}}", "inspector.drawer-split-view-state": "{\"horizontal\":{\"size\":0,\"showMode\":\"OnlyMain\"}}", "inspectorVersion": "38", "releaseNoteVersionSeen": "78", "styles-pane-sidebar-tab-order": "{\"styles\":10,\"computed\":20}"}, "synced_preferences_sync_disabled": {"adorner-settings": "[{\"adorner\":\"grid\",\"isEnabled\":true},{\"adorner\":\"subgrid\",\"isEnabled\":true},{\"adorner\":\"flex\",\"isEnabled\":true},{\"adorner\":\"ad\",\"isEnabled\":true},{\"adorner\":\"scroll-snap\",\"isEnabled\":true},{\"adorner\":\"container\",\"isEnabled\":true},{\"adorner\":\"slot\",\"isEnabled\":true},{\"adorner\":\"top-layer\",\"isEnabled\":true},{\"adorner\":\"reveal\",\"isEnabled\":true},{\"adorner\":\"media\",\"isEnabled\":false},{\"adorner\":\"scroll\",\"isEnabled\":true}]", "syncedInspectorVersion": "38"}}, "domain_diversity": {"last_reporting_timestamp": "13395105903771218"}, "enterprise_profile_guid": "248f49e7-f911-4b49-831f-b80b4063cef5", "extensions": {"alerts": {"initialized": true}, "chrome_url_overrides": {}, "commands": {}, "install_signature": {"expire_date": "2025-09-14", "ids": ["ghbmnnjooekpmoecnnnilnnbdlolhkhi"], "invalid_ids": [], "salt": "/IbCzQ+fEnuqg5c02RyNtbDs/GWbbCvaktFCP4ETl1A=", "signature": "Uijo4Qg99y5Nht9kDasd81NZU2fH80YPpsuAJSoclas6UNAJ833fvlV3zVF4T/espD8t+sdb+LPTCQhMxWDeC6BYTjAnTXaGnifmb2Ac9wiAGTFJh25OPO3wfNoEqHPkYEdu9lLSWpOnrrKWZUJP47jlVWk2mXCG3yg2NLRUwFf9Lc2/oWAwgVQDhXmEL11fba34GlmlqKVVz0G+wrfKtSBtES7Gpw16NPMnXfQHZxxxdtipamPcPHXz28i/yJJjMygTyfPWgtuk3UJFgJWaJZK8wx7TJdypNJDHxS/ZjIWF1q89i+UeEzMWrNL+iGtCCrNVXv1EvbxvGgHQToJIEw==", "signature_format_version": 2, "timestamp": "*****************"}}, "gaia_cookie": {"changed_time": **********.006533, "hash": "aRRwV2LJYWK/RmnblFBOsQmE76Y=", "last_list_accounts_data": "[\"gaia.l.a.r\",[[\"gaia.l.a\",1,\"Liquid One\",\"<EMAIL>\",\"https://lh3.googleusercontent.com/-w2_-vDLQ8qw/AAAAAAAAAAI/AAAAAAAAAAA/65ohMxhTF7w/s48-c/photo.jpg\",1,1,0,null,1,\"115880785246047860713\",null,null,null,null,1]]]"}, "gcm": {"product_category_for_subtypes": "com.chrome.windows", "push_messaging_unsubscribed_entries_list": []}, "google": {"services": {"signin": {"LAST_SIGNIN_ACCESS_POINT": {"time": "2025-06-22T22:50:52.727Z", "value": "54"}, "REFRESH_TOKEN_RECEIVED": {"time": "2025-06-22T22:50:50.127Z", "value": "Successful (115880785246047860713)"}}, "signin_scoped_device_id": "7ef0259a-a8dd-43db-bbf6-5ea30b0585ba"}}, "in_product_help": {"new_badge": {"Compose": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "ComposeProactiveNudge": {"feature_enabled_time": "*****************", "show_count": 0, "used_count": 0}, "Glic": {"feature_enabled_time": "13395106307424879", "show_count": 0, "used_count": 0}, "GlicAppMenuNewBadge": {"feature_enabled_time": "13395106307424884", "show_count": 0, "used_count": 0}, "GlicKeyboardShortcutNewBadge": {"feature_enabled_time": "13395106307424882", "show_count": 0, "used_count": 0}, "LensOverlay": {"feature_enabled_time": "13395105903700521", "show_count": 0, "used_count": 0}, "PasswordManualFallbackAvailable": {"feature_enabled_time": "13395106307424870", "show_count": 0, "used_count": 0}}, "recent_session_enabled_time": "13395105903699877", "recent_session_start_times": ["13395105903699877"], "session_last_active_time": "13395114296945669", "session_start_time": "13395105903699877"}, "intl": {"selected_languages": "en-US,en"}, "invalidation": {"per_sender_client_id_cache": {"1013309121859": "cI8zytZB5to"}, "per_sender_registered_for_invalidation": {"1013309121859": {}}, "per_sender_topics_to_handler": {"1013309121859": {}}}, "language_model_counters": {"en": 17}, "media": {"device_id_salt": "B1314DBFE2E4599EF286034B7F93EB1C", "engagement": {"schema_version": 5}}, "media_router": {"receiver_id_hash_token": "2gOPTkgE3R5AJ72v7H+5G+xuaduWUHRBXAhJTwkrNkwRSmh7oaHwT96JizoSR7+JeHENDf8OAUX5uEEiIaKEpA=="}, "ntp": {"num_personal_suggestions": 4}, "optimization_guide": {"hintsfetcher": {"hosts_successfully_fetched": {}}, "predictionmodelfetcher": {"last_fetch_attempt": "13395114309928082", "last_fetch_success": "13395114310102626"}, "previous_optimization_types_with_filter": {"AMERICAN_EXPRESS_CREDIT_CARD_FLIGHT_BENEFITS": true, "AMERICAN_EXPRESS_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "AUTOFILL_ABLATION_SITES_LIST1": true, "AUTOFILL_ABLATION_SITES_LIST2": true, "AUTOFILL_ABLATION_SITES_LIST3": true, "AUTOFILL_ABLATION_SITES_LIST4": true, "AUTOFILL_ABLATION_SITES_LIST5": true, "AUTOFILL_PREDICTION_IMPROVEMENTS_ALLOWLIST": true, "BMO_CREDIT_CARD_AIR_MILES_PARTNER_BENEFITS": true, "BMO_CREDIT_CARD_ALCOHOL_STORE_BENEFITS": true, "BMO_CREDIT_CARD_DINING_BENEFITS": true, "BMO_CREDIT_CARD_DRUGSTORE_BENEFITS": true, "BMO_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "BMO_CREDIT_CARD_GROCERY_BENEFITS": true, "BMO_CREDIT_CARD_OFFICE_SUPPLY_BENEFITS": true, "BMO_CREDIT_CARD_RECURRING_BILL_BENEFITS": true, "BMO_CREDIT_CARD_TRANSIT_BENEFITS": true, "BMO_CREDIT_CARD_TRAVEL_BENEFITS": true, "BMO_CREDIT_CARD_WHOLESALE_CLUB_BENEFITS": true, "BUY_NOW_PAY_LATER_ALLOWLIST_AFFIRM": true, "BUY_NOW_PAY_LATER_ALLOWLIST_ZIP": true, "CAPITAL_ONE_CREDIT_CARD_BENEFITS_BLOCKED": true, "CAPITAL_ONE_CREDIT_CARD_DINING_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_GROCERY_BENEFITS": true, "CAPITAL_ONE_CREDIT_CARD_STREAMING_BENEFITS": true, "EWALLET_MERCHANT_ALLOWLIST": true, "GLIC_ACTION_PAGE_BLOCK": true, "HISTORY_CLUSTERS": true, "HISTORY_EMBEDDINGS": true, "IBAN_AUTOFILL_BLOCKED": true, "PIX_MERCHANT_ORIGINS_ALLOWLIST": true, "PIX_PAYMENT_MERCHANT_ALLOWLIST": true, "SHARED_CREDIT_CARD_DINING_BENEFITS": true, "SHARED_CREDIT_CARD_ENTERTAINMENT_BENEFITS": true, "SHARED_CREDIT_CARD_FLIGHT_BENEFITS": true, "SHARED_CREDIT_CARD_GROCERY_BENEFITS": true, "SHARED_CREDIT_CARD_STREAMING_BENEFITS": true, "SHARED_CREDIT_CARD_SUBSCRIPTION_BENEFITS": true, "SHOPPING_PAGE_PREDICTOR": true, "TEXT_CLASSIFIER_ENTITY_DETECTION": true, "VCN_MERCHANT_OPT_OUT_DISCOVER": true, "VCN_MERCHANT_OPT_OUT_MASTERCARD": true, "VCN_MERCHANT_OPT_OUT_VISA": true}, "previously_registered_optimization_types": {"ABOUT_THIS_SITE": true, "COMPOSE": true, "GLIC_CONTEXTUAL_CUEING": true, "GLIC_ZERO_STATE_SUGGESTIONS": true, "HISTORY_CLUSTERS": true, "LOADING_PREDICTOR": true, "MERCHANT_TRUST_SIGNALS_V2": true, "PAGE_ENTITIES": true, "PRICE_INSIGHTS": true, "PRICE_TRACKING": true, "SALIENT_IMAGE": true, "SHOPPING_DISCOUNTS": true, "SHOPPING_PAGE_TYPES": true, "V8_COMPILE_HINTS": true}, "store_file_paths_to_delete": {}}, "password_manager": {"account_store_migrated_to_os_crypt_async": true, "autofillable_credentials_account_store_login_database": false, "autofillable_credentials_profile_store_login_database": false, "profile_store_migrated_to_os_crypt_async": true}, "privacy_sandbox": {"fake_notice": {"first_sign_in_time": "*****************", "prompt_shown_time": "*****************", "prompt_shown_time_sync": "*****************"}, "first_party_sets_data_access_allowed_initialized": true}, "profile": {"avatar_index": 26, "background_password_check": {"check_fri_weight": 9, "check_interval": "*************", "check_mon_weight": 6, "check_sat_weight": 6, "check_sun_weight": 6, "check_thu_weight": 9, "check_tue_weight": 9, "check_wed_weight": 9, "next_check_time": "*****************"}, "content_settings": {"exceptions": {"3pcd_heuristics_grants": {}, "3pcd_support": {}, "abusive_notification_permissions": {}, "access_to_get_all_screens_media_in_session": {}, "anti_abuse": {}, "app_banner": {}, "ar": {}, "are_suspicious_notifications_allowlisted_by_user": {}, "auto_picture_in_picture": {}, "auto_select_certificate": {}, "automatic_downloads": {}, "automatic_fullscreen": {}, "autoplay": {}, "background_sync": {}, "bluetooth_chooser_data": {}, "bluetooth_guard": {}, "bluetooth_scanning": {}, "camera_pan_tilt_zoom": {}, "captured_surface_control": {}, "client_hints": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://accounts.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://gds.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://myaccount.google.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://studio.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}, "https://www.youtube.com:443,*": {"last_modified": "*****************", "setting": {"client_hints": [9, 10, 11, 13, 14, 16, 23, 25, 29]}}}, "clipboard": {}, "controlled_frame": {}, "cookie_controls_metadata": {"https://[*.]google.com,*": {"last_modified": "*****************", "setting": {}}, "https://[*.]youtube.com,*": {"last_modified": "*****************", "setting": {}}}, "cookies": {}, "direct_sockets": {}, "direct_sockets_private_network_access": {}, "display_media_system_audio": {}, "disruptive_notification_permissions": {}, "durable_storage": {}, "fedcm_idp_registration": {}, "fedcm_idp_signin": {"https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"chosen-objects": [{"idp-origin": "https://accounts.google.com", "idp-signin-status": true}]}}}, "fedcm_share": {}, "file_system_access_chooser_data": {}, "file_system_access_extended_permission": {}, "file_system_access_restore_permission": {}, "file_system_last_picked_directory": {}, "file_system_read_guard": {}, "file_system_write_guard": {}, "formfill_metadata": {}, "geolocation": {}, "hand_tracking": {}, "hid_chooser_data": {}, "hid_guard": {}, "http_allowed": {}, "https_enforced": {}, "idle_detection": {}, "images": {}, "important_site_info": {}, "insecure_private_network": {}, "intent_picker_auto_display": {}, "javascript": {}, "javascript_jit": {}, "javascript_optimizer": {}, "keyboard_lock": {}, "legacy_cookie_access": {}, "legacy_cookie_scope": {}, "local_fonts": {}, "local_network_access": {}, "media_engagement": {"https://accounts.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}, "https://gds.google.com:443,*": {"expiration": "*****************", "last_modified": "*****************", "lifetime": "*************", "setting": {"hasHighScore": false, "lastMediaPlaybackTime": 0.0, "mediaPlaybacks": 0, "visits": 1}}}, "media_stream_camera": {}, "media_stream_mic": {}, "midi_sysex": {}, "mixed_script": {}, "nfc_devices": {}, "notification_interactions": {}, "notification_permission_review": {}, "notifications": {}, "password_protection": {}, "payment_handler": {}, "permission_autoblocking_data": {}, "permission_autorevocation_data": {}, "pointer_lock": {}, "popups": {}, "private_network_chooser_data": {}, "private_network_guard": {}, "protected_media_identifier": {}, "protocol_handler": {}, "reduced_accept_language": {}, "safe_browsing_url_check_data": {}, "sensors": {}, "serial_chooser_data": {}, "serial_guard": {}, "site_engagement": {"chrome://newtab/,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395114300033672e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}, "https://accounts.google.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.3395106249263476e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 8.***************, "rawScore": 8.***************}}, "https://studio.youtube.com:443,*": {"last_modified": "*****************", "setting": {"lastEngagementTime": 1.339511437138473e+16, "lastShortcutLaunchTime": 0.0, "pointsAddedToday": 15.0, "rawScore": 15.0}}}, "sound": {}, "speaker_selection": {}, "ssl_cert_decisions": {}, "storage_access": {}, "storage_access_header_origin_trial": {}, "subresource_filter": {}, "subresource_filter_data": {}, "third_party_storage_partitioning": {}, "top_level_3pcd_origin_trial": {}, "top_level_3pcd_support": {}, "top_level_storage_access": {}, "tracking_protection": {}, "unused_site_permissions": {}, "usb_chooser_data": {}, "usb_guard": {}, "vr": {}, "web_app_installation": {}, "webid_api": {}, "webid_auto_reauthn": {}, "window_placement": {}}, "pref_version": 1}, "created_by_version": "137.0.7151.120", "creation_time": "13395105903680056", "did_work_around_bug_364820109_default": true, "did_work_around_bug_364820109_exceptions": true, "exit_type": "Crashed", "family_member_role": "not_in_family", "last_engagement_time": "13395114371384730", "last_time_obsolete_http_credentials_removed": 1750632363.707642, "last_time_password_store_metrics_reported": 1750632333.693293, "managed": {"locally_parent_approved_extensions": {}, "locally_parent_approved_extensions_migration_state": 1}, "managed_user_id": "", "name": "Your Chrome", "password_hash_data_list": [{"hash": "djEweyLC3w40tHe8HFzUXHUq64Ue6TeZNJVNe45JjS5pNu02q8hj2HLl", "is_gaia": "djEwURz0NIxW5qm2UMyt/HYms6bqvaqhYAs3WU3k1p71fIY=", "last_signin": 1750632639.233762, "salt_length": "djEwJWX8HcDkdz+3ntZf+VqTBTDKWWiujUdP3KRMydPvDkC6EsP9/e0Tev/JvPnZ+g==", "username": "djEwUArUsSPBGATUXzKFoRfRj5rq+aNEsuj0koMAKpvq1qHE4OlLSn+TJjAjeacK7A1YzVCX5mNN2A=="}], "were_old_google_logins_removed": true}, "safebrowsing": {"advanced_protection_last_refresh": "13395114300000538", "event_timestamps": {}, "hash_real_time_ohttp_expiration_time": "13395365103931029", "hash_real_time_ohttp_key": "2AAgETtEl5cjzDpksr9J2MiGvUsUaS87sxbZ6vEn9Mxqv18ABAABAAI=", "metrics_last_log_time": "13395105903", "next_password_capture_event_log_time": "13397271633", "scout_reporting_enabled_when_deprecated": false}, "safety_hub": {"unused_site_permissions_revocation": {"migration_completed": true}}, "saved_tab_groups": {"did_enable_shared_tab_groups_in_last_session": false, "specifics_to_data_migration": true}, "segmentation_platform": {"client_result_prefs": "ClIKDXNob3BwaW5nX3VzZXISQQo2DQAAAAAQosiwi/LY5RcaJAocChoNAAAAPxIMU2hvcHBpbmdVc2VyGgVPdGhlchIEEAIYBCADEMTIsIvy2OUX", "device_switcher_util": {"result": {"labels": ["AndroidPhone"]}}, "last_db_compaction_time": "13395023999000000", "uma_in_sql_start_time": "13395105903693042"}, "sessions": {"event_log": [{"crashed": true, "time": "13395110846823898", "type": 0}, {"crashed": true, "time": "13395110966024956", "type": 0}, {"crashed": true, "time": "13395111359949251", "type": 0}, {"crashed": true, "time": "13395111474039114", "type": 0}, {"crashed": true, "time": "13395111572975457", "type": 0}, {"crashed": true, "time": "13395111745060280", "type": 0}, {"crashed": true, "time": "13395111917204376", "type": 0}, {"crashed": true, "time": "13395112025558614", "type": 0}, {"crashed": true, "time": "13395112183848597", "type": 0}, {"crashed": true, "time": "13395112296572649", "type": 0}, {"crashed": true, "time": "13395112514381213", "type": 0}, {"crashed": true, "time": "13395112640592007", "type": 0}, {"crashed": true, "time": "13395112778093714", "type": 0}, {"crashed": true, "time": "13395112879198053", "type": 0}, {"crashed": true, "time": "13395112975691089", "type": 0}, {"crashed": true, "time": "13395113111316386", "type": 0}, {"crashed": true, "time": "13395113488326857", "type": 0}, {"crashed": true, "time": "13395113660999406", "type": 0}, {"crashed": true, "time": "13395114299928175", "type": 0}], "session_data_status": 1}, "settings": {"force_google_safesearch": false}, "sharing": {"fcm_registration": {"registration_timestamp": "133951143********"}, "local_sharing_info": {"enabled_features": [4, 8], "sender_id_target_info": {"device_auth_secret": "+t8TMnfVBR/dE7rXL+fGqw==", "device_fcm_token": "dWo-uvu4j2Y:APA91bFkFqGXJHhJwXeboB5BZwtd2oUX7ZcQ1c3uaE_6EoAGedkX8IcxXu-4yhfTcBmYES8zNmkJH4NKAAL6lM-YqjIzzXq6iey3afRP6fIZho5dEWAdh2M", "device_p256dh": "BN7ytrp/QUPCUlpJVxO1xxsdQRENQhAYNAGhYgCITlZ7dBsJ0sr6xsPp8Av/PwqpQmhCvwnPrJkQygN86qo+MZo="}, "vapid_target_info": {"device_auth_secret": "", "device_fcm_token": "", "device_p256dh": ""}}}, "signin": {"accounts_metadata_dict": {"115880785246047860713": {"BookmarksExplicitBrowserSigninEnabled": false, "ChromeSigninInterceptionUserChoice": 2, "ExtensionsExplicitBrowserSigninEnabled": false}}, "allowed": true, "cookie_clear_on_exit_migration_notice_complete": true, "explicit_browser_signin": true, "signin_with_explicit_browser_signin_on": true}, "spellcheck": {"dictionaries": ["en-US"], "dictionary": ""}, "sync": {"cached_passphrase_type": 2, "cached_persistent_auth_error": false, "cached_trusted_vault_auto_upgrade_experiment_group": "", "data_type_status_for_sync_to_signin": {"app_list": false, "app_settings": false, "apps": false, "arc_package": false, "autofill": false, "autofill_profiles": false, "autofill_valuable": false, "autofill_wallet": false, "autofill_wallet_credential": false, "autofill_wallet_metadata": false, "autofill_wallet_offer": false, "autofill_wallet_usage": false, "bookmarks": false, "collaboration_group": false, "contact_info": false, "cookies": false, "device_info": false, "dictionary": false, "extension_settings": false, "extensions": false, "history": false, "history_delete_directives": false, "incoming_password_sharing_invitation": false, "managed_user_settings": false, "nigori": false, "os_preferences": false, "os_priority_preferences": false, "outgoing_password_sharing_invitation": false, "passwords": false, "plus_address": false, "plus_address_setting": false, "power_bookmark": false, "preferences": false, "printers": false, "printers_authorization_servers": false, "priority_preferences": false, "product_comparison": false, "reading_list": false, "saved_tab_group": false, "search_engines": false, "security_events": false, "send_tab_to_self": false, "sessions": false, "shared_tab_group_account_data": false, "shared_tab_group_data": false, "sharing_message": false, "themes": false, "user_consent": false, "user_events": false, "web_apps": false, "webapks": false, "webauthn_credential": false, "wifi_configurations": false, "workspace_desk": false}, "encryption_bootstrap_token_per_account_migration_done": true, "feature_status_for_sync_to_signin": 5, "gaia_id": "115880785246047860713", "local_device_guids_with_timestamp": [{"cache_guid": "sHVrz7LVSV1dXHqesKqlUA==", "timestamp": 155036}], "passwords_per_account_pref_migration_done": true, "transport_data_per_account": {"U1y2lOPfd6wGH2e36MShC53YZkIM0OcnnMhC2SXgCSY=": {"sync.bag_of_chips": "Cn8SfUNocm9tZSBXSU4gMTM3LjAuNzE1MS4xMjAgKDcwNzI2OWI5MDNiOWQ2NmRjZmMwNmVhNTEwMWVlYzNjZjdjZGIxMmItcmVmcy9icmFuY2gtaGVhZHMvNzE1MUB7IzIzNTd9KSBjaGFubmVsKHN0YWJsZSksZ3ppcChnZmUp", "sync.birthday": "z0000017d-eddb-c1f9-0000-000061c61b96", "sync.cache_guid": "sHVrz7LVSV1dXHqesKqlUA==", "sync.last_poll_time": "*****************", "sync.last_synced_time": "*****************", "sync.short_poll_interval": "***********"}}}, "syncing_theme_prefs_migrated_to_non_syncing": true, "tab_group_saves_ui_update_migrated": true, "toolbar": {"pinned_cast_migration_complete": true, "pinned_chrome_labs_migration_complete": true, "tab_search_migration_complete": true}, "total_passwords_available_for_account": 0, "total_passwords_available_for_profile": 0, "translate_site_blacklist": [], "translate_site_blocklist_with_time": {}, "updateclientdata": {"apps": {"ghbmnnjooekpmoecnnnilnnbdlolhkhi": {"cohort": "1::", "cohortname": "", "dlrc": 6747, "installdate": 6747, "pf": "444622b3-79e6-4dd3-8a08-6604496ecae7"}}}, "web_apps": {"did_migrate_default_chrome_apps": ["MigrateDefaultChromeAppToWebAppsGSuite", "MigrateDefaultChromeAppToWebAppsNonGSuite"], "last_preinstall_synchronize_version": "137", "migrated_default_apps": ["aohghmighlieiainnegkcijnfilokake", "aapocclcgogkmnckokdopfmhonfmgoek", "felcaaldnbdncclmgdcncolpebgiejap", "apdfllckaahabafndbhieahigkjlhalf", "pjkljhegncpnkpknbcohdijeoejaedia", "blpcfgokakmgnkcojhhkbfbldkacnbeo"]}, "zerosuggest": {"cachedresults": ")]}'\n[\"\",[\"sitaare zameen par box office collection\",\"nba warriors\",\"console nintendo switch\",\"atlantic hurricane season\",\"costco executive membership cost\",\"ice detention honeymoon\",\"billy strings\",\"san francisco giants vs boston red sox\"],[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\"],[],{\"google:clientdata\":{\"bpc\":false,\"tlw\":false},\"google:groupsinfo\":\"ChoIkk4SFQoRVHJlbmRpbmcgc2VhcmNoZXMoCg\\u003d\\u003d\",\"google:suggestdetail\":[{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"zl\":10002},{\"google:entityinfo\":\"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\\u003d\\u003d\",\"zl\":10002},{\"zl\":10002}],\"google:suggesteventid\":\"4645741736108300327\",\"google:suggestrelevance\":[601,600,555,554,553,552,551,550],\"google:suggestsubtypes\":[[3,143,362,308,10],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308],[3,143,362,308]],\"google:suggesttype\":[\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"QUERY\",\"ENTITY\",\"QUERY\"],\"google:verbatimrelevance\":851}]"}}